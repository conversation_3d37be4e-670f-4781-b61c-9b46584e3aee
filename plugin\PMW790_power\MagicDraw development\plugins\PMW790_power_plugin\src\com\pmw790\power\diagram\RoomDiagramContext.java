package com.pmw790.power.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.openapi.uml.PresentationElementsManager;
import com.nomagic.magicdraw.openapi.uml.ReadOnlyElementException;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.pmw790.power.functions.ConnectionRegistry;
import com.pmw790.power.functions.Utilities;

import java.util.*;
import static com.pmw790.power.functions.Utilities.Log;

/**
 * Context class holding information needed for room power diagram creation
 * This manages multiple cabinets within a room
 */
public class RoomDiagramContext {
    private final Project project;
    private final Class roomBlock;
    private final ConnectionRegistry registry;
    private final String roomName;
    private final List<String> cabinetNames;
    private final Map<String, CabinetDiagramContext> cabinetContexts;
    private final Set<String> cabinetsWithProviders;
    private final Map<String, List<String>> providerHierarchyCache;
    private final Map<String, Property> roomPowerProviderPropertiesCache;

    // Cabinet consumer cache for efficient consumer lookups
    private final Map<String, Map<String, Property>> cabinetConsumerCache;

    // Track unconnected cabinet consumers (consumers not connected to internal cabinet providers)
    private final Map<String, Set<String>> unconnectedCabinetConsumers;

    // Track cabinet presentation elements for efficient consumer placement
    private final Map<String, PresentationElement> cabinetPresentationElements;

    // Track cabinets that have consumers (regardless of whether they have providers)
    private final Set<String> cabinetsWithConsumers;

    /**
     * Creates a new context for room diagram operations
     */
    public RoomDiagramContext(Project project, Class roomBlock, ConnectionRegistry registry) {
        this.project = project;
        this.roomBlock = roomBlock;
        this.registry = registry;
        this.roomName = roomBlock.getName();
        this.cabinetNames = registry.getCabinetsForRoom(roomName);
        this.cabinetContexts = new HashMap<>();
        this.cabinetsWithProviders = new HashSet<>();
        this.providerHierarchyCache = new HashMap<>();
        this.roomPowerProviderPropertiesCache = new HashMap<>();
        this.cabinetConsumerCache = new HashMap<>();
        this.unconnectedCabinetConsumers = new HashMap<>();
        this.cabinetPresentationElements = new HashMap<>();
        this.cabinetsWithConsumers = new HashSet<>();

        // Pre-cache provider hierarchy for faster access
        precacheProviderHierarchy();

        // Pre-identify cabinets with providers for faster filtering
        identifyCabinetsWithProviders();

        // Pre-cache cabinet consumers for efficient consumer lookups
        initializeCabinetConsumerCache();
    }

    /**
     * Pre-caches the provider hierarchy data for this room
     */
    private void precacheProviderHierarchy() {
        Map<String, Map<String, List<String>>> roomToProvidersMap = registry.getRoomToPowerProviders();
        Map<String, List<String>> roomHierarchy = roomToProvidersMap.get(roomName);

        if (roomHierarchy != null) {
            providerHierarchyCache.putAll(roomHierarchy);
        }
    }

    /**
     * Pre-identifies cabinets that have power providers with consumers
     */
    private void identifyCabinetsWithProviders() {
        // If there are no cabinets, we can return early
        if (cabinetNames.isEmpty()) {
            return;
        }

        for (String cabinetName : cabinetNames) {
            List<String> providers = registry.getCabinetToPowerProviders().getOrDefault(cabinetName, Collections.emptyList());
            if (!providers.isEmpty()) {
                cabinetsWithProviders.add(cabinetName);
            }
        }
    }

    /**
     * Pre-caches cabinet consumer properties for efficient consumer lookups
     * This builds a map of cabinet name to consumer properties within that cabinet
     * Also identifies cabinets that have consumers (regardless of whether they have providers)
     */
    private void initializeCabinetConsumerCache() {
        // If there are no cabinets, we can return early
        if (cabinetNames.isEmpty()) {
            return;
        }

        for (String cabinetName : cabinetNames) {
            Map<String, Property> cabinetConsumers = new HashMap<>();

            // Get all properties for this cabinet from the cache
            Map<String, Property> cabinetProperties = Utilities.CabinetProperties.getCabinetPropertiesFromCache(cabinetName);

            if (cabinetProperties != null) {
                // Filter for power consumers only
                for (Map.Entry<String, Property> entry : cabinetProperties.entrySet()) {
                    String propertyName = entry.getKey();
                    Property property = entry.getValue();

                    // Check if this property is a power consumer
                    if (property != null && property.getType() != null) {
                        String typeName = property.getType().getName();
                        String powerType = Utilities.getPowerType(typeName);

                        if (Utilities.TYPE_POWER_CONSUMER.equals(powerType)) {
                            cabinetConsumers.put(propertyName, property);
                        }
                    }
                }
            }

            // Store the consumer properties for this cabinet (even if empty)
            cabinetConsumerCache.put(cabinetName, cabinetConsumers);

            // Track cabinets that have consumers
            if (!cabinetConsumers.isEmpty()) {
                cabinetsWithConsumers.add(cabinetName);
            }
        }
    }

    /**
     * Gets all cabinet contexts for this room
     * @return List of CabinetDiagramContext objects
     */
    public List<CabinetDiagramContext> getAllCabinetContexts() {
        List<CabinetDiagramContext> contexts = new ArrayList<>();

        for (String cabinetName : cabinetNames) {
            CabinetDiagramContext context = getCabinetContext(cabinetName);
            if (context != null) {
                contexts.add(context);
            }
        }

        return contexts;
    }

    /**
     * Gets a CabinetDiagramContext for a specific cabinet in the room
     * This reuses the pre-cached properties for efficiency
     *
     * @param cabinetName The name of the cabinet
     * @return A CabinetDiagramContext for the cabinet, or null if not found
     */
    public CabinetDiagramContext getCabinetContext(String cabinetName) {
        // Check if we already have a context for this cabinet
        if (cabinetContexts.containsKey(cabinetName)) {
            return cabinetContexts.get(cabinetName);
        }

        try {
            // Use cached cabinet block instead of looking it up again
            Class cabinetBlock = registry.getCabinetBlockByName(cabinetName);
            if (cabinetBlock != null) {
                // Get properties from the global cache
                Map<String, Property> properties = Utilities.CabinetProperties.getCabinetPropertiesFromCache(cabinetName);

                // Create context with pre-cached properties
                CabinetDiagramContext context = new CabinetDiagramContext(
                        project, cabinetBlock, registry, true, roomName, properties);

                // Store in cache
                cabinetContexts.put(cabinetName, context);
                return context;
            } else {
                Log("Warning: Cabinet block not found in cache for " + cabinetName + " in room " + roomName);
            }
        } catch (Exception e) {
            Log("Error creating cabinet context for " + cabinetName + ": " + e.getMessage());
        }

        return null;
    }

    /**
     * @return The MagicDraw project
     */
    public Project getProject() {
        return project;
    }

    /**
     * @return Number of cabinets in this room
     */
    public int getCabinetCount() {
        return cabinetNames.size();
    }

    /**
     * @return Whether this room has any cabinets with power providers that have consumers
     */
    public boolean hasCabinetsWithProviders() {
        return !cabinetsWithProviders.isEmpty();
    }

    /**
     * Finds the cabinet containing a specific consumer in this room
     * Uses the pre-cached cabinet consumer data for efficient lookup
     *
     * @param consumerName The name of the consumer to find
     * @return The cabinet name containing the consumer, or null if not found in any cabinet
     */
    public String getCabinetContainingConsumer(String consumerName) {
        return findCabinetForConsumer(consumerName);
    }

    /**
     * Gets all consumers in a specific cabinet
     *
     * @param cabinetName The cabinet name
     * @return Map of consumer names to Property objects, or empty map if cabinet not found
     */
    public Map<String, Property> getConsumersInCabinet(String cabinetName) {
        if (cabinetName == null) {
            return new HashMap<>();
        }

        Map<String, Property> consumers = cabinetConsumerCache.get(cabinetName);
        return consumers != null ? new HashMap<>(consumers) : new HashMap<>();
    }

    /**
     * Gets all unconnected consumers in a specific cabinet
     * These are consumers that are not connected to internal cabinet providers
     *
     * @param cabinetName The cabinet name
     * @return Set of unconnected consumer names, or empty set if cabinet not found
     */
    public Set<String> getUnconnectedConsumersInCabinet(String cabinetName) {
        if (cabinetName == null) {
            return new HashSet<>();
        }

        Set<String> unconnected = unconnectedCabinetConsumers.get(cabinetName);
        return unconnected != null ? new HashSet<>(unconnected) : new HashSet<>();
    }

    /**
     * Gets all cabinets that have unconnected consumers
     *
     * @return Set of cabinet names that have consumers not connected to internal providers
     */
    public Set<String> getCabinetsWithUnconnectedConsumers() {
        return new HashSet<>(unconnectedCabinetConsumers.keySet());
    }

    /**
     * Adds cabinets and room-level power providers to the room diagram
     * Processing order: 1) Cabinets with providers, 2) Identify all unconnected consumers, 3) Room-level providers with cabinet consumer connections
     *
     * @param diagram The diagram to add elements to
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    public void addToRoomDiagram(DiagramPresentationElement diagram) throws ReadOnlyElementException {
        if (diagram == null) {
            Log("Error: Cannot add elements to null diagram");
            return;
        }

        // STEP 1: Add cabinets with providers first (this identifies unconnected consumers in those cabinets)
        addCabinetsWithProvidersToRoomDiagram(diagram);

        // STEP 2: Identify unconnected consumers in ALL cabinets (including consumer-only cabinets)
        identifyAllUnconnectedConsumers();

        // STEP 3: Add room-level power elements and connect to unconnected cabinet consumers
        addPowerElementsToRoomDiagram(diagram);
    }

    /**
     * Adds room-level power elements (providers with their hierarchy and associated consumers) to the diagram
     * Gets both power providers and power consumers from getRoomNonCabinetProperties() and adds providers
     * along with any consumers connected to those providers found in the room
     *
     * @param diagram The diagram to add power elements to
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addPowerElementsToRoomDiagram(DiagramPresentationElement diagram) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // Get all non-cabinet properties once (includes both providers and consumers)
        Map<String, Property> allNonCabinetProps = Utilities.getRoomNonCabinetProperties(roomName);

        // Get room-level power provider properties, passing the already-retrieved non-cabinet properties
        Map<String, Property> roomProviderProps = getRoomPowerProviderProperties(allNonCabinetProps);

        // Add room-level providers to diagram
        for (Map.Entry<String, Property> entry : roomProviderProps.entrySet()) {
            String providerName = entry.getKey();
            Property providerProperty = entry.getValue();

            try {
                PresentationElement providerPE = manager.createShapeElement(providerProperty, diagram, true);

                // Add child providers under this parent using cached hierarchy
                addChildProvidersToParent(diagram, providerPE, providerName, providerProperty);

                // Add consumers connected to this provider that are found in the room
                addConsumersForProvider(diagram, providerName, allNonCabinetProps);

            } catch (Exception e) {
                Log("Error adding room-level provider " + providerName + " to diagram: " + e.getMessage());
            }
        }
    }

    /**
     * Gets room-level power provider properties from the room element cache
     * Optimized version that uses pre-retrieved non-cabinet properties
     *
     * @param nonCabinetProps Pre-retrieved non-cabinet properties for the room
     * @return Map of provider name to Property object for room-level providers
     */
    private Map<String, Property> getRoomPowerProviderProperties(Map<String, Property> nonCabinetProps) {
        // Use cached data if available
        if (!roomPowerProviderPropertiesCache.isEmpty()) {
            return new HashMap<>(roomPowerProviderPropertiesCache);
        }

        Map<String, Property> roomProviderProps = new HashMap<>();

        // Get parent providers from cached hierarchy
        Set<String> roomParentProviders = providerHierarchyCache.keySet();

        if (roomParentProviders.isEmpty()) {
            return roomProviderProps;
        }

        // Filter for power providers that are parent providers in this room
        for (Map.Entry<String, Property> entry : nonCabinetProps.entrySet()) {
            String elementName = entry.getKey();
            if (roomParentProviders.contains(elementName)) {
                roomProviderProps.put(elementName, entry.getValue());
            }
        }

        // Cache the results for future use
        roomPowerProviderPropertiesCache.putAll(roomProviderProps);

        return roomProviderProps;
    }

    /**
     * Adds child power providers under their parent provider
     * OPTIMIZED: Uses pre-retrieved parent provider property to avoid redundant lookups
     *
     * @param diagram The diagram
     * @param parentPE The parent provider presentation element
     * @param parentProviderName The parent provider name
     * @param parentProviderProperty The parent provider property (already retrieved)
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addChildProvidersToParent(DiagramPresentationElement diagram, PresentationElement parentPE,
                                           String parentProviderName, Property parentProviderProperty) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // Use cached hierarchy data instead of fetching from registry again
        List<String> childProviders = providerHierarchyCache.get(parentProviderName);

        if (childProviders != null && !childProviders.isEmpty()) {
            for (String childProviderName : childProviders) {
                try {
                    // Get the parent provider block to find child part properties
                    Set<String> allProviders = registry.getPowerProviders();
                    if (allProviders.contains(parentProviderName)) {
                        // Use the already-retrieved parent provider property
                        if (parentProviderProperty != null && parentProviderProperty.getType() instanceof Class) {
                            Class parentProviderBlock = (Class) parentProviderProperty.getType();

                            // Find child provider part property in parent provider block
                            Property childProviderProperty = Utilities.ModelElements.findPropertyByName(parentProviderBlock, childProviderName);

                            if (childProviderProperty != null) {
                                manager.createShapeElement(childProviderProperty, parentPE, true);
                            }
                        }
                    }
                } catch (Exception e) {
                    Log("Error adding power provider " + childProviderName + " under parent " + parentProviderName + ": " + e.getMessage());
                }
            }
        }
    }

    /**
     * Adds power consumers connected to a specific provider to the diagram
     * Handles both room-level consumers and unconnected cabinet consumers
     *
     * @param diagram The diagram to add consumers to
     * @param providerName The provider name to find consumers for
     * @param allNonCabinetProps All non-cabinet properties in the room (includes consumers)
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addConsumersForProvider(DiagramPresentationElement diagram, String providerName,
                                       Map<String, Property> allNonCabinetProps) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // Get consumers connected to this provider from the registry
        List<String> connectedConsumers = registry.getConsumersForProvider(providerName);

        // Process each connected consumer
        for (String consumerName : connectedConsumers) {
            Property consumerProperty = null;
            PresentationElement targetParent = diagram;

            // First, try to find the consumer in room-level non-cabinet properties
            consumerProperty = allNonCabinetProps.get(consumerName);

            // If not found in room-level properties, check if it's an unconnected cabinet consumer
            if (consumerProperty == null) {
                String cabinetName = findCabinetContainingUnconnectedConsumer(consumerName);
                if (cabinetName != null) {
                    consumerProperty = getConsumerFromCabinet(cabinetName, consumerName);

                    // Use the existing cabinet presentation element (created during cabinet processing)
                    if (consumerProperty != null) {
                        targetParent = cabinetPresentationElements.get(cabinetName);
                        if (targetParent == null) {
                            // Fallback: create cabinet presentation element if not found
                            targetParent = findOrCreateCabinetPresentationElement(diagram, cabinetName);
                        }
                    }
                }
            }

            // Add the consumer if found (either in room or cabinet)
            if (consumerProperty != null && targetParent != null) {
                try {
                    manager.createShapeElement(consumerProperty, targetParent, true);
                } catch (Exception e) {
                    Log("Error adding consumer " + consumerName + " for provider " + providerName + " to diagram: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Finds the cabinet that hosts a specific power provider
     *
     * @param providerName The provider name
     * @return The cabinet name or null if not found
     */
    private String findCabinetForProvider(String providerName) {
        Map<String, List<String>> cabinetProviders = registry.getCabinetToPowerProviders();

        for (Map.Entry<String, List<String>> entry : cabinetProviders.entrySet()) {
            String cabinetName = entry.getKey();
            List<String> providers = entry.getValue();

            if (providers.contains(providerName)) {
                return cabinetName;
            }
        }

        return null;
    }

    /**
     * Finds the cabinet that contains a specific power consumer using the pre-cached consumer data
     *
     * @param consumerName The consumer name to search for
     * @return The cabinet name containing the consumer, or null if not found
     */
    private String findCabinetForConsumer(String consumerName) {
        if (consumerName == null) {
            return null;
        }

        // Search through the cached cabinet consumers
        for (Map.Entry<String, Map<String, Property>> cabinetEntry : cabinetConsumerCache.entrySet()) {
            String cabinetName = cabinetEntry.getKey();
            Map<String, Property> consumers = cabinetEntry.getValue();

            if (consumers.containsKey(consumerName)) {
                return cabinetName;
            }
        }

        return null;
    }

    /**
     * Gets the consumer property from a specific cabinet using the pre-cached data
     *
     * @param cabinetName The cabinet name
     * @param consumerName The consumer name
     * @return The consumer Property object, or null if not found
     */
    private Property getConsumerFromCabinet(String cabinetName, String consumerName) {
        if (cabinetName == null || consumerName == null) {
            return null;
        }

        Map<String, Property> cabinetConsumers = cabinetConsumerCache.get(cabinetName);
        if (cabinetConsumers != null) {
            return cabinetConsumers.get(consumerName);
        }

        return null;
    }

    /**
     * Finds the cabinet containing a specific unconnected consumer
     * This searches only in the unconnected consumers cache for efficiency
     *
     * @param consumerName The consumer name to search for
     * @return The cabinet name containing the unconnected consumer, or null if not found
     */
    private String findCabinetContainingUnconnectedConsumer(String consumerName) {
        if (consumerName == null) {
            return null;
        }

        // Search through unconnected cabinet consumers
        for (Map.Entry<String, Set<String>> entry : unconnectedCabinetConsumers.entrySet()) {
            String cabinetName = entry.getKey();
            Set<String> unconnectedConsumers = entry.getValue();

            if (unconnectedConsumers.contains(consumerName)) {
                return cabinetName;
            }
        }

        return null;
    }

    /**
     * Finds or creates a cabinet presentation element in the diagram
     * This ensures that consumers inside cabinets are properly nested under their cabinet
     *
     * @param diagram The room diagram
     * @param cabinetName The cabinet name
     * @return The cabinet presentation element, or null if cabinet cannot be found/created
     */
    private PresentationElement findOrCreateCabinetPresentationElement(DiagramPresentationElement diagram, String cabinetName) {
        if (diagram == null || cabinetName == null) {
            return null;
        }

        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // First, check if the cabinet presentation element already exists in the diagram
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe.getElement() instanceof Property) {
                Property property = (Property) pe.getElement();
                if (cabinetName.equals(property.getName())) {
                    return pe;
                }
            }
        }

        // If not found, create a new cabinet presentation element
        try {
            Map<String, Property> roomCabinetProps = Utilities.getRoomCabinetProperties(roomName);
            Property cabinetProperty = roomCabinetProps.get(cabinetName);

            if (cabinetProperty != null) {
                return manager.createShapeElement(cabinetProperty, diagram, true);
            }
        } catch (Exception e) {
            Log("Error creating cabinet presentation element for " + cabinetName + ": " + e.getMessage());
        }

        return null;
    }

    /**
     * Adds cabinets with power providers to the room diagram and identifies unconnected consumers
     * This method processes cabinets first and tracks consumers not connected to internal providers
     *
     * @param diagram The diagram to add cabinets to
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addCabinetsWithProvidersToRoomDiagram(DiagramPresentationElement diagram) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // Use the pre-identified cabinets with providers
        for (String cabinetName : cabinetsWithProviders) {
            try {
                // Add cabinet shape - get cabinet property from room's cabinet properties
                Map<String, Property> roomCabinetProps = Utilities.getRoomCabinetProperties(roomName);
                Property cabinetProperty = roomCabinetProps.get(cabinetName);
                CabinetDiagramContext cabinetContext = getCabinetContext(cabinetName);

                if (cabinetProperty != null && cabinetContext != null) {
                    PresentationElement cabinetPE = manager.createShapeElement(cabinetProperty, diagram, true);

                    // Store cabinet presentation element for later consumer placement
                    cabinetPresentationElements.put(cabinetName, cabinetPE);

                    // Add internal providers to the cabinet
                    List<String> providers = cabinetContext.getProviders();
                    for (String providerName : providers) {
                        Property providerProperty = cabinetContext.getPartProperty(providerName);
                        if (providerProperty != null) {
                            manager.createShapeElement(providerProperty, cabinetPE, true);
                        }
                    }

                    // Identify unconnected consumers in this cabinet
                    identifyUnconnectedConsumersInCabinet(cabinetName, cabinetContext);
                }
            } catch (Exception e) {
                Log("Error adding cabinet " + cabinetName + " to diagram: " + e.getMessage());
                // Continue with other cabinets
            }
        }
    }

    /**
     * Identifies consumers in a cabinet that are not connected to internal cabinet providers
     * These consumers may need to be connected to room-level providers
     *
     * @param cabinetName The cabinet name
     * @param cabinetContext The cabinet context containing provider and consumer information
     */
    private void identifyUnconnectedConsumersInCabinet(String cabinetName, CabinetDiagramContext cabinetContext) {
        if (cabinetName == null || cabinetContext == null) {
            return;
        }

        // Get all consumers in this cabinet
        Map<String, Property> cabinetConsumers = cabinetConsumerCache.get(cabinetName);
        if (cabinetConsumers == null || cabinetConsumers.isEmpty()) {
            return;
        }

        // Get all providers in this cabinet
        List<String> cabinetProviders = cabinetContext.getProviders();
        Set<String> connectedConsumers = new HashSet<>();

        // Find consumers connected to internal cabinet providers
        for (String providerName : cabinetProviders) {
            List<String> consumersForProvider = registry.getConsumersForProvider(providerName);
            connectedConsumers.addAll(consumersForProvider);
        }

        // Identify unconnected consumers (consumers in cabinet but not connected to internal providers)
        Set<String> unconnectedConsumers = new HashSet<>();
        for (String consumerName : cabinetConsumers.keySet()) {
            if (!connectedConsumers.contains(consumerName)) {
                unconnectedConsumers.add(consumerName);
            }
        }

        // Store unconnected consumers for this cabinet
        if (!unconnectedConsumers.isEmpty()) {
            unconnectedCabinetConsumers.put(cabinetName, unconnectedConsumers);
            Log("Cabinet " + cabinetName + " has " + unconnectedConsumers.size() + " unconnected consumers: " + unconnectedConsumers);
        }
    }

    /**
     * Identifies unconnected consumers in ALL cabinets (including consumer-only cabinets)
     * This method handles cabinets that have consumers but no internal providers
     */
    private void identifyAllUnconnectedConsumers() {
        // Process all cabinets that have consumers (regardless of whether they have providers)
        for (String cabinetName : cabinetsWithConsumers) {
            // Skip cabinets that were already processed (those with providers)
            if (unconnectedCabinetConsumers.containsKey(cabinetName)) {
                continue;
            }

            // Get all consumers in this cabinet
            Map<String, Property> cabinetConsumers = cabinetConsumerCache.get(cabinetName);
            if (cabinetConsumers == null || cabinetConsumers.isEmpty()) {
                continue;
            }

            // Get cabinet context to check for providers
            CabinetDiagramContext cabinetContext = getCabinetContext(cabinetName);
            if (cabinetContext == null) {
                continue;
            }

            // Get all providers in this cabinet
            List<String> cabinetProviders = cabinetContext.getProviders();
            Set<String> connectedConsumers = new HashSet<>();

            // Find consumers connected to internal cabinet providers (if any)
            for (String providerName : cabinetProviders) {
                List<String> consumersForProvider = registry.getConsumersForProvider(providerName);
                connectedConsumers.addAll(consumersForProvider);
            }

            // Identify unconnected consumers (consumers in cabinet but not connected to internal providers)
            Set<String> unconnectedConsumers = new HashSet<>();
            for (String consumerName : cabinetConsumers.keySet()) {
                if (!connectedConsumers.contains(consumerName)) {
                    unconnectedConsumers.add(consumerName);
                }
            }

            // Store unconnected consumers for this cabinet
            if (!unconnectedConsumers.isEmpty()) {
                unconnectedCabinetConsumers.put(cabinetName, unconnectedConsumers);
                Log("Cabinet " + cabinetName + " (consumer-only) has " + unconnectedConsumers.size() + " unconnected consumers: " + unconnectedConsumers);
            }
        }
    }
}



