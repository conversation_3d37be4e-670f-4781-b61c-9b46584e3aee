package com.pmw790.power.main;

import com.nomagic.magicdraw.core.Application;
import com.pmw790.power.functions.ConnectionRegistry;
import com.pmw790.power.diagram.PowerConnectorManager;
import com.pmw790.power.schema.BindingSchemaManager;
import com.pmw790.power.functions.SysMLStereotypes;
import com.pmw790.power.configurators.PowerBrowserConfigurator;
import com.nomagic.magicdraw.plugins.Plugin;
import com.nomagic.magicdraw.actions.ActionsConfiguratorsManager;
import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.core.project.ProjectEventListenerAdapter;
import com.pmw790.power.functions.Utilities;

import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Logger;

public class PMW790Plugin extends Plugin
{
	// Track the current project to detect project switching
	private final AtomicReference<Project> currentProjectRef = new AtomicReference<>(null);

	// Flag to indicate if we're in the middle of a project switch
	private volatile boolean projectSwitchInProgress = false;

	// Logger for this class
	private static final Logger LOGGER = Logger.getLogger(PMW790Plugin.class.getName());

	// Plugin prefix for console logs
	private static final String LOG_PREFIX = "[PMW790Plugin] ";

	@Override
	public void init()
	{
		ActionsConfiguratorsManager.getInstance().addContainmentBrowserContextConfigurator(
				PowerBrowserConfigurator.INSTANCE);

		Application.getInstance().getProjectsManager().addProjectListener(
				new ProjectEventListenerAdapter() {
					@Override
					public void projectOpened(Project project) {
						try {
							// Set the switch flag to prevent concurrent operations
							projectSwitchInProgress = true;

							// Get project name for logging
							String projectName = project != null ? project.getName() : "null";

							// Check if this is actually a project switch
							Project previousProject = currentProjectRef.getAndSet(project);
							String previousProjectName = previousProject != null ? previousProject.getName() : "null";

							if (previousProject != null && previousProject != project) {
								// This is a project switch
								LOGGER.info(LOG_PREFIX + "Detected project switch from " + previousProjectName + " to " + projectName);

								// Show popup notification for project switch
								Utilities.showProjectSwitchNotification(previousProjectName, projectName);
							} else {
								// Normal project open
								LOGGER.info(LOG_PREFIX + "Project opened: " + projectName);
							}

							// Activate or initialize the project cache
							activateProjectCache(project);
						} catch (Exception e) {
							LOGGER.severe(LOG_PREFIX + "Error in projectOpened: " + e.getMessage());
							e.printStackTrace();

							// Try to recover by clearing all caches for this specific project
							clearAllCaches(project);

							// Try to activate the project cache
							try {
								activateProjectCache(project);
							} catch (Exception ex) {
								LOGGER.severe(LOG_PREFIX + "Failed to recover: " + ex.getMessage());
							}
						} finally {
							// Reset the switch flag
							projectSwitchInProgress = false;
						}
					}

					@Override
					public void projectClosed(Project project) {
						try {
							// Set the switch flag to prevent concurrent operations
							projectSwitchInProgress = true;

							// Update current project reference
							currentProjectRef.set(null);

							// Get project name for logging
							String projectName = project != null ? project.getName() : "null";

							// Log the project closure
							LOGGER.info(LOG_PREFIX + "Project closed: " + projectName);

							// Perform a full cache clear for the closed project
							performFullCacheClear(project);

							// Log project closure completion
							if (project != null) {
								LOGGER.info(LOG_PREFIX + "Completed cleanup for project: " + projectName);
							}
						} catch (Exception e) {
							LOGGER.severe(LOG_PREFIX + "Error in projectClosed: " + e.getMessage());
							e.printStackTrace();

							// Try to recover by clearing all caches for this specific project
							clearAllCaches(project);
						} finally {
							// Reset the switch flag
							projectSwitchInProgress = false;
						}
					}

					private void clearAllCaches(Project specificProject) {
						// Use the specified project or get current project if none specified
						Project projectToUse = specificProject != null ?
							specificProject : Application.getInstance().getProject();

						// Clear all global caches in order
						Utilities.clearCaches();
						SysMLStereotypes.clearCaches();
						ConnectionRegistry.getInstance().reset();
						BindingSchemaManager.getInstance().clearCache();

						// Clear connector cache in PowerConnectorManager
						PowerConnectorManager.clearConnectorCache();

						// Clear project-specific caches if we have a project to use
						if (projectToUse != null) {
							Utilities.ModelElements.clearProjectCaches(projectToUse);
						}

						// Force garbage collection to clean up any lingering references
						System.gc();

						// Log cache clearing
						LOGGER.info(LOG_PREFIX + "Cleared all caches" +
							(projectToUse != null ? " for project: " + projectToUse.getName() : ""));
					}

					/**
					 * Helper method to clear all caches in the plugin using the current project
					 * This ensures consistent cache clearing across all event handlers
					 */
					private void clearAllCaches() {
						clearAllCaches(null);
					}

					/**
					 * Performs a full cache clear, including project-specific caches
					 * This is more thorough than clearAllCaches() and is used during project switching
					 */
					private void performFullCacheClear(Project oldProject) {
						// Clear all caches specifically for the old project
						if (oldProject != null) {
							// First clear all caches for this specific project
							clearAllCaches(oldProject);

							// Make sure project-specific caches are cleared
							try {
								Utilities.ModelElements.clearProjectCaches(oldProject);
								LOGGER.info(LOG_PREFIX + "Cleared project-specific caches for: " + oldProject.getName());
							} catch (Exception e) {
								LOGGER.warning(LOG_PREFIX + "Error clearing project-specific caches: " + e.getMessage());
							}
						} else {
							// If no specific project, clear all general caches
							clearAllCaches();
						}

						// Reset SysMLStereotypes state
						SysMLStereotypes.clearCaches();

						// Force garbage collection twice to ensure all references are cleared
						System.gc();
						System.gc();

						LOGGER.info(LOG_PREFIX + "Performed full cache clear");
					}

					/**
					 * Initializes a project's cache
					 * This method is called whenever a project is opened or switched to
					 */
					private void activateProjectCache(Project project) {
						if (project == null) {
							LOGGER.warning(LOG_PREFIX + "Cannot activate cache for null project");
							return;
						}

						String projectName = project.getName();

						// Always clear all caches when activating a project to prevent duplicates
						LOGGER.info(LOG_PREFIX + "Clearing all caches before activating project: " + projectName);
						clearAllCaches(project);

						// Initialize the project
						LOGGER.info(LOG_PREFIX + "Initializing cache for project: " + projectName);

						try {
							// Initialize SysMLStereotypes with the project
							SysMLStereotypes.initialize(project);
							LOGGER.info(LOG_PREFIX + "Initialized SysMLStereotypes for: " + projectName);

							// Find and cache Power Provider and Power Consumer blocks
							Utilities.findPowerBlocks();
							LOGGER.info(LOG_PREFIX + "Cached Power Provider and Power Consumer blocks");

							// Find and cache room blocks early for optimization
							Utilities.findAndCacheRoomBlocks(project);
							LOGGER.info(LOG_PREFIX + "Cached room blocks for: " + projectName);

							// Initialize binding schema from model
							BindingSchemaManager.getInstance().initialize(project);
							LOGGER.info(LOG_PREFIX + "Initialized BindingSchemaManager for: " + projectName);

							// Analyze model connections
							ConnectionRegistry.getInstance().analyzeModelConnections(project);
							LOGGER.info(LOG_PREFIX + "Analyzed model connections for: " + projectName);

						} catch (Exception e) {
							LOGGER.severe(LOG_PREFIX + "Error initializing project: " + e.getMessage());
						}
					}

				});
	}

	@Override
	public boolean close()
	{
		ActionsConfiguratorsManager.getInstance().removeContainmentBrowserContextConfigurator(
				PowerBrowserConfigurator.INSTANCE);
		return true;
	}

	@Override
	public boolean isSupported()
	{
		return true;
	}
}